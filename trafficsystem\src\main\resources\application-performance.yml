# 性能优化配置文件
# 用于四方向智能交通分析系统的性能调优

spring:
  # 数据库连接池优化
  data:
    mongodb:
      # 连接池配置
      options:
        # 最大连接数
        max-pool-size: 50
        # 最小连接数
        min-pool-size: 10
        # 连接超时时间（毫秒）
        connect-timeout: 10000
        # Socket超时时间（毫秒）
        socket-timeout: 30000
        # 服务器选择超时时间（毫秒）
        server-selection-timeout: 5000
        # 最大等待时间（毫秒）
        max-wait-time: 10000
        # 最大连接空闲时间（毫秒）
        max-connection-idle-time: 300000
        # 最大连接生存时间（毫秒）
        max-connection-life-time: 600000
        # 心跳频率（毫秒）
        heartbeat-frequency: 10000
        # 最小心跳频率（毫秒）
        min-heartbeat-frequency: 500

  # GridFS配置优化
  servlet:
    multipart:
      # 最大文件大小（四方向视频上传）
      max-file-size: 500MB
      # 最大请求大小（四个视频文件总大小）
      max-request-size: 2GB
      # 文件写入磁盘的阈值
      file-size-threshold: 10MB
      # 临时文件位置
      location: ${java.io.tmpdir}/four-way-uploads

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m

  # 异步处理配置
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 8
        # 最大线程数
        max-size: 20
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: four-way-analysis-
        # 线程保活时间
        keep-alive: 60s
      shutdown:
        # 优雅关闭
        await-termination: true
        await-termination-period: 60s

# 服务器性能配置
server:
  # Tomcat优化
  tomcat:
    # 最大连接数
    max-connections: 8192
    # 接受队列长度
    accept-count: 100
    # 最大工作线程数
    threads:
      max: 200
      min-spare: 10
    # 连接超时时间
    connection-timeout: 20000
    # 最大HTTP头大小
    max-http-header-size: 8KB
    # 最大HTTP POST大小
    max-http-form-post-size: 2MB

# 日志配置优化
logging:
  level:
    # 四方向分析相关日志
    com.traffic.analysis.service: INFO
    com.traffic.analysis.controller: INFO
    # MongoDB日志
    org.springframework.data.mongodb: WARN
    # WebSocket日志
    org.springframework.web.socket: INFO
    # 性能相关日志
    org.springframework.cache: DEBUG
  pattern:
    # 控制台日志格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{taskId}] %logger{36} - %msg%n"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{taskId}] %logger{36} - %msg%n"

# 四方向分析系统特定配置
four-way-analysis:
  # 视频处理配置
  video:
    # 最大并发处理数
    max-concurrent-processing: 4
    # 单个视频最大处理时间（分钟）
    max-processing-time: 30
    # 帧提取间隔（秒）
    frame-extraction-interval: 1
    # 临时文件清理间隔（小时）
    temp-file-cleanup-interval: 2
    # 最大缓存帧数
    max-cached-frames: 100

  # WebSocket配置
  websocket:
    # 最大连接数
    max-connections: 500
    # 消息缓冲区大小
    message-buffer-size: 8192
    # 心跳间隔（秒）
    heartbeat-interval: 30
    # 连接超时时间（秒）
    connection-timeout: 300

  # 缓存配置
  cache:
    # 帧数据缓存大小
    frame-cache-size: 1000
    # 分析结果缓存时间（分钟）
    result-cache-duration: 60
    # 任务状态缓存时间（分钟）
    task-status-cache-duration: 10

  # 性能监控配置
  monitoring:
    # 启用性能监控
    enabled: true
    # 监控间隔（秒）
    interval: 30
    # 性能指标保留时间（小时）
    metrics-retention: 24

# JVM性能调优建议（通过启动参数设置）
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UseStringDeduplication
# -XX:+OptimizeStringConcat
# -Djava.awt.headless=true

# 系统资源监控阈值
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: four-way-traffic-analysis

# 数据库性能监控
spring.jpa.properties.hibernate:
  # 启用统计信息
  generate_statistics: true
  # 慢查询阈值（毫秒）
  session.events.log.LOG_QUERIES_SLOWER_THAN_MS: 1000
